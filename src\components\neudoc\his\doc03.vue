<template>
  <div class="container">
    <!-- 2.检查申请 -->
    <el-container direction="horizontal">

      <!-- 编辑弹框---start -->
      <el-dialog
        v-model="dialogFormVisible"
        title="新增医技"
        width="80%"
        class="medical-tech-dialog"
        :modal="true"
        :append-to-body="true"
        :close-on-click-modal="false"
      >
        <el-row>
          <el-col :span="18">
            <div class="grid-content bg-purple">
              <el-input placeholder="请输入医技助记码" size="mini" v-model="keywords01"></el-input>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content bg-purple">
              <el-button type="primary" size="mini" style="margin-left: 10px" icon="el-icon-view"
                @click="searchData">查询</el-button>
            </div>
          </el-col>
        </el-row>
        <el-table 
          stripe 
          ref="multipleTable" 
          size="mini" 
          :data="categories" 
          tooltip-effect="dark" 
          style="width: 100%"
          highlight-current-row
          @row-click="handleRowClick"
        >
          <el-table-column label="项目编码" prop="ItemCode" width="140" align="left"></el-table-column>
          <el-table-column label="项目名称" prop="ItemName" align="left"></el-table-column>
          <el-table-column label="项目规格" prop="Format" width="80" align="left"></el-table-column>
          <el-table-column label="项目单价" prop="Price" width="80" align="left"></el-table-column>
          <el-table-column label="拼音助记码" prop="MnemonicCode" width="180" align="left"></el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="action-column">
                        <template #default="scope">
                          <el-button 
                            type="primary" 
                            size="mini" 
                            @click="handleSelectMedicalTech(scope.row)"
                            style="
                              margin: 2px; 
                              padding: 4px 8px;
                              font-weight: bold;
                              border-radius: 4px;
                              box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                              transition: all 0.3s;
                              min-width: 50px;
                              white-space: nowrap;
                              overflow: hidden;
                              text-overflow: ellipsis;
                            "
                            :style="{
                              'background-color': '#409EFF',
                              'border-color': '#409EFF'
                            }"
                            @mouseover="handleMouseOver($event)"
                            @mouseout="handleMouseOut($event)"
                          >选择医技项目</el-button>
                          <span class="button-hint" style="
                            display: inline-block;
                            margin-left: 5px;
                            font-size: 12px;
                            color: #999;
                          ">(点击选择)</span>
                        </template>
                      </el-table-column>
        </el-table>
        <el-row>
          <el-col :span="24" align="right">
            <el-pagination background :page-size="pageSize" :current-page.sync="currentPage01"
              layout="prev, pager, next" :total="totalCount" @current-change="currentChange"
              v-show="this.categories.length > 0">
            </el-pagination>
          </el-col>
        </el-row>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="dialogFormVisible = false" size="mini">关闭</el-button>
        </div>
      </el-dialog>
      <!-- 编辑弹框---end -->

      <!-- 保存模板弹框 - 优化版 -->
      <el-dialog 
        v-model="templateDialogVisible" 
        title="保存为模板" 
        width="30%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form 
          :model="templateForm" 
          size="mini"
          :rules="templateRules"
          ref="templateFormRef"
        >
          <el-form-item label="模板名称" prop="name">
            <el-input 
              v-model="templateForm.name" 
              placeholder="请输入模板名称(2-20个字符)"
              maxlength="20"
              show-word-limit
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="模板范围" prop="scope">
            <el-radio-group v-model="templateForm.scope">
              <el-radio label="1">个人</el-radio>
              <el-radio label="2">科室</el-radio>
              <el-radio label="3">全院</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button 
              type="primary" 
              @click="submitTemplateForm"
              :loading="templateSaving"
              :disabled="templateSaving"
            >
              <span v-if="!templateSaving">保存</span>
              <span v-else>保存中...</span>
            </el-button>
            <el-button 
              @click="cancelTemplateForm"
              :disabled="templateSaving"
            >取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>

      <!-- 添加详情弹框 -->
      <el-dialog v-model="innerVisible" width="30%" title="添加详情" append-to-body center>
        <el-form label-width="100px" :model="formEdit02" class="demo-form-inline" size="mini">
          <el-form-item label="申请名称">
            <el-input v-model="formEdit02.Name" placeholder="申请名称"></el-input>
          </el-form-item>
          <el-form-item label="项目名称">
            <el-tag type="info">{{ Temp.TempItemName }}</el-tag>
          </el-form-item>
          <el-form-item label="目的要求">
            <el-input v-model="formEdit02.Objective" placeholder="检查部位"></el-input>
          </el-form-item>
          <el-form-item label="检查部位">
            <el-input v-model="formEdit02.Position" placeholder="检查部位"></el-input>
          </el-form-item>
          <el-form-item label="是否加急">
            <el-switch v-model="formEdit02.IsUrgent" active-text="是" inactive-text="否"></el-switch>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" style="width: 100px;" @click="addCheckDetailed">添加</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>

      <!-- 页面正文 -->
      <el-aside style="width: 700px;height: 500px;">
        <el-row style="background-color: #EAF1F5">
          <el-col :span="24">
            <el-button type="text" size="mini" class="el-icon-circle-plus"
                @click="showFme">新增项目</el-button>
            <el-button type="text" size="mini" class="el-icon-remove"
                :disabled="this.selItems.length == 0" @click="deleteAll">删除项目</el-button>
            <el-button type="text" size="mini" class="el-icon-success"
                :disabled="this.selItems.length == 0" @click="upToSaved">开立项目</el-button>
            <el-button type="text" size="mini" class="el-icon-delete"
                :disabled="this.selItems.length == 0" @click="upToBad">作废项目</el-button>
            <el-button type="text" size="mini" class="el-icon-circle-plus-outline"
                :disabled="this.selItems.length == 0" @click="handleSaveAsTemplate">存为组套*</el-button>
            <el-button type="text" size="small" class="el-icon-circle-plus-outline"
                @click="refApply">刷新</el-button>
            <el-button type="text" size="small" class="el-icon-info"
                @click="debugDepartmentData">调试科室</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-tag size="mini">本项目金额合计：</el-tag>
            <el-tag type="warning" size="mini">{{ sumPrice }}元</el-tag>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22" style="margin-top: 4px;">
            <el-table ref="singleTable" :data="tableData02" style="width: 100%;margin: 0px;padding: 0px;"
              :show-header="true" size="mini" @selection-change="handleSelectionChange">
              <el-table-column type="selection">
              </el-table-column>
              <el-table-column property="Name" label="申请名称" width="100">
              </el-table-column>
              <el-table-column property="ItemName" label="项目名称">
              </el-table-column>
              <el-table-column property="DeptName" label="执行科室" width="100">
              </el-table-column>
              <el-table-column property="State" label="执行状态" width="100">
                <template #default="scope">
                  <span v-if="scope?.row?.State !== undefined">
                    <span v-if="scope.row.State == 1 || scope.row.State == '1'">暂存</span>
                    <span v-else-if="scope.row.State == 2 || scope.row.State == '2'">已开立</span>
                    <span v-else-if="scope.row.State == 3 || scope.row.State == '3'">已执行</span>
                    <span v-else-if="scope.row.State == 0 || scope.row.State == '0'">作废</span>
                    <span v-else>未知状态({{ scope.row.State }})</span>
                  </span>
                  <span v-else>加载中...</span>
                </template>
              </el-table-column>
              <el-table-column property="Price" label="单价" width="80">
                <template #default="scope">
                  <span v-if="scope?.row?.Price !== undefined">
                    {{ scope.row.Price }}
                  </span>
                  <span v-else>--</span>
                </template>
              </el-table-column>
              <el-table-column property="Result" label="检查结果" width="80">
                <template slot-scope="scope">
                  <el-popover 
                    placement="right" 
                    width="400" 
                    :content="scope?.row?.Result || '暂无结果'" 
                    trigger="click"
                    :disabled="!scope?.row?.Result"
                  >
                    <el-button 
                      slot="reference" 
                      type="text" 
                      size="mini"
                      :disabled="!scope?.row?.Result"
                    >
                      {{ scope?.row?.Result ? '查看详细' : '--' }}
                    </el-button>
                  </el-popover>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-aside>
      <el-main style="margin-top: -20px;">
        <el-tabs type="card">
          <el-tab-pane label="常用模板">
            <div class="template-container">
              <el-table
                :data="tableData04"
                style="width: 100%"
                ref="singleTable"
                size="small"
                :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
                :row-style="{ height: '45px' }"
                stripe
                border>

                <el-table-column prop="name" label="模板名称" min-width="200">
                  <template #default="scope">
                    <div class="template-name">
                      <i class="el-icon-document" style="color: #409EFF; margin-right: 8px;"></i>
                      <span>{{ scope.row.name || scope.row.Name || '未命名模板' }}</span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column prop="scope" label="范围" width="100" align="center">
                  <template #default="scope">
                    <el-tag
                      :type="scope.row.scope === 'personal' ? 'primary' : 'success'"
                      size="small">
                      {{ scope.row.scope === 'personal' ? '个人' : '科室' }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="recordType" label="类型" width="80" align="center">
                  <template #default="scope">
                    <span class="record-type">检查</span>
                  </template>
                </el-table-column>

                <el-table-column label="创建时间" width="150" align="center">
                  <template #default="scope">
                    <span class="create-time">
                      <i class="el-icon-time" style="margin-right: 4px;"></i>
                      {{ formatDate(scope.row.creationTime || scope.row.createTime) }}
                    </span>
                  </template>
                </el-table-column>

                <el-table-column fixed="right" label="操作" width="140" align="center">
                  <template #default="scope">
                    <el-button
                      @click.prevent="addTmplChecks(scope.row)"
                      type="primary"
                      size="mini"
                      icon="el-icon-check"
                      plain>
                      使用
                    </el-button>
                    <el-button
                      @click.prevent="showTmplChecks(scope.row)"
                      type="info"
                      size="mini"
                      icon="el-icon-view"
                      plain>
                      详细
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 空状态 -->
              <div v-if="!tableData04 || tableData04.length === 0" class="empty-state">
                <i class="el-icon-document-copy" style="font-size: 48px; color: #C0C4CC;"></i>
                <p style="color: #909399; margin-top: 16px;">暂无常用模板</p>
                <p style="color: #C0C4CC; font-size: 12px;">保存检查申请为模板后将在此显示</p>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <el-dialog title="模板详细" :visible.sync="applyDialogShow" width="40%">
          <el-table stripe ref="multipleTable" size="mini" :data="categories02" tooltip-effect="dark"
            style="width: 100%">
            <el-table-column label="项目编码" prop="ItemCode" width="140" align="left"></el-table-column>
            <el-table-column label="项目名称" prop="ItemName" align="left"></el-table-column>
            <el-table-column label="项目规格" prop="Format" width="80" align="left"></el-table-column>
            <el-table-column label="检查部位" prop="Position" width="80" align="left"></el-table-column>
          </el-table>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="applyDialogShow = false" size="mini">关闭</el-button>
          </div>
        </el-dialog>
      </el-main>
    </el-container>

  </div>
</template>

<style scoped>
.template-container {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-name {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #303133;
}

.template-name:hover {
  color: #409EFF;
  cursor: pointer;
}

.record-type {
  color: #67C23A;
  font-weight: 500;
  font-size: 12px;
}

.create-time {
  color: #909399;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #fafafa;
  border-radius: 8px;
  margin-top: 16px;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table .el-table__header-wrapper {
  border-radius: 8px 8px 0 0;
}

.el-table .el-table__body-wrapper {
  border-radius: 0 0 8px 8px;
}

/* 按钮样式优化 */
.el-button--mini {
  padding: 5px 12px;
  font-size: 12px;
  border-radius: 4px;
  margin: 0 2px;
}

.el-button--mini.is-plain:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 标签样式 */
.el-tag {
  border-radius: 12px;
  font-size: 11px;
  padding: 2px 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-container {
    padding: 8px;
  }

  .el-table .el-table__header-wrapper th,
  .el-table .el-table__body-wrapper td {
    padding: 8px 4px;
  }
}
</style>

<script>
import axios from 'axios'
import { showMsgTitle } from '../../../utils/api'
import { showMsgConfirm } from '../../../utils/api'
import {
  postReq,
  postJsonRequest,
  putRequest,
  deleteRequest as deleteReq,
  getReq
} from '../../../utils/api'
import { addLabApply } from '../../../utils/billUtils'
export default {
  mounted: function () {
    // 添加更严格的空值检查和调试信息
    const register = this.$store?.state?.Register || {};
    console.log('=== doc03 mounted 调试信息 ===');
    console.log('Store Register状态:', register);
    console.log('患者登记ID:', register.ID);
    console.log('用户ID:', register.UserID);

    // 检查患者登记ID
    if (!register.ID) {
      console.warn('缺少患者登记ID，尝试从其他来源获取...');

      // 尝试从sessionStorage获取
      try {
        const patientStr = sessionStorage.getItem('medicalRecord');
        if (patientStr && patientStr !== '{}') {
          const patient = JSON.parse(patientStr);
          const registId = patient.RegistID || patient.ID;
          if (registId) {
            console.log('从sessionStorage获取到患者登记ID:', registId);
            this.loadApply(registId, '', 1);
          } else {
            console.error('无法获取患者登记ID，使用默认值');
            this.loadApply('', '', 1);
          }
        } else {
          console.error('sessionStorage中没有患者信息');
          this.loadApply('', '', 1);
        }
      } catch (error) {
        console.error('解析患者信息失败:', error);
        this.loadApply('', '', 1);
      }
    } else {
      this.loadApply(register.ID, '', 1);
    }

    this.loadTemplateByDoc(register.UserID || 1, 1, '');
    this.loadSessionPatient();
    this.loadDepartments(); // 加载科室数据
  },
  methods: {
    // 格式化状态文本
    formatState(state) {
      console.log('格式化状态文本，state:', state, typeof state);
      if (state == '1' || state === 1) {
        return '暂存';
      } else if (state == '2' || state === 2) {
        return '已开立';
      } else if (state == '3' || state === 3) {
        return '已执行';
      } else if (state == '0' || state === 0) {
        return '作废';
      } else {
        return '未知状态(' + state + ')';
      }
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '--';
      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return '--';

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
      } catch (error) {
        return '--';
      }
    },

    // PUT JSON请求方法
    putJsonRequest(url, params) {
      return axios({
        method: 'put',
        url: `http://localhost:8080${url}`,
        data: params,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    },

    // 根据项目ID获取价格信息
    async getItemPriceById(itemId) {
      try {
        console.log('正在获取项目价格，ItemID:', itemId);

        // 从医技项目中获取价格信息 - 尝试多种查询方式
        console.log('查询URL:', `/fmeditem/page?count=50&pn=1&keyword=${itemId}`);
        let response = await getReq(`/fmeditem/page?count=50&pn=1&keyword=${itemId}`);
        console.log('医技项目查询响应:', response);

        // 如果第一次查询没有结果，尝试不使用keyword参数
        if (response.status === 200 && response.data && response.data.data &&
            (!response.data.data.records || response.data.data.records.length === 0)) {
          console.log('第一次查询无结果，尝试获取所有医技项目进行匹配');
          response = await getReq(`/fmeditem/page?count=200&pn=1`);
          console.log('获取所有医技项目响应:', response);
        }

        if (response.status === 200 && response.data) {
          console.log('医技项目查询完整响应数据:', response.data);

          // 检查不同的数据结构
          let records = null;
          if (response.data.data && response.data.data.records) {
            records = response.data.data.records;
            console.log('使用data.records字段:', records);
          } else if (response.data.records) {
            records = response.data.records;
            console.log('使用records字段:', records);
          } else if (Array.isArray(response.data)) {
            records = response.data;
            console.log('直接使用data数组:', records);
          } else if (Array.isArray(response.data.data)) {
            records = response.data.data;
            console.log('使用data字段数组:', records);
          }

          if (records && records.length > 0) {
            console.log('医技项目查询结果:', records);

            // 查找匹配的项目 - 改进匹配逻辑
            let matchedItem = null;
            console.log('开始查找匹配项目，目标ID:', itemId, '搜索结果数量:', records.length);

            for (const item of records) {
              console.log('检查项目:', {
                id: item.id,
                ID: item.ID,
                itemCode: item.itemCode,
                ItemCode: item.ItemCode,
                itemName: item.itemName,
                ItemName: item.ItemName,
                price: item.price,
                Price: item.Price
              });

              // 尝试多种ID匹配方式
              if (item.id == itemId || item.ID == itemId ||
                  item.itemId == itemId || item.ItemID == itemId) {
                matchedItem = item;
                console.log('找到ID匹配的项目:', matchedItem);
                break;
              }
            }

            // 如果没有找到精确匹配，但有搜索结果，取第一个项目
            if (!matchedItem && records.length > 0) {
              matchedItem = records[0];
              console.log('使用第一个搜索结果作为匹配项目:', matchedItem);
            }

            if (matchedItem) {
              console.log('最终使用的医技项目:', matchedItem);
              const price = matchedItem.price || matchedItem.Price || 0;
              console.log('提取的价格:', price);
              return {
                price: Number(price),
                itemName: matchedItem.itemName || matchedItem.ItemName,
                deptName: matchedItem.deptName || matchedItem.DeptName
              };
            } else {
              console.log('未找到匹配的医技项目');
            }
          } else {
            console.log('医技项目查询结果为空');
          }
        } else {
          console.log('医技项目查询失败或响应格式不正确');
        }
      } catch (error) {
        console.error('获取项目价格信息失败:', error);
      }
      return null;
    },

    refApply() {
      console.log('=== 刷新申请数据 ===');
      this.loading = true;

      // 添加更严格的空值检查
      const register = this.$store?.state?.Register || {};
      console.log('当前患者登记信息:', register);

      // 刷新申请数据
      this.loadApply(register.ID || '', '', 1);

      // 刷新模板数据
      this.loadTemplateByDoc(register.UserID || 1, 1, '');

      // 刷新患者信息
      this.loadSessionPatient();

      // 刷新医技项目数据
      this.loadFmeDatas(1, this.pageSize, this.keywords01);

      // 重新加载科室数据
      this.loadDepartments();

      this.loading = false;
      this.$message({
        type: 'success',
        message: '数据刷新完成',
        duration: 2000
      });
    },
    showConfirm(msg) {
      showMsgConfirm(this, msg);
    },
    showMsg(msg) {
      showMsgTitle(this, msg);
    },

    // 新增方法 - 处理医技项目选择
    handleSelectMedicalTech(row) {
      console.log('handleSelectMedicalTech called', row);

      this.innerVisible = true;
      this.Temp.TempItemName = row.ItemName;
      this.formEdit02.ItemID = row.ID;
      // 自动设置Name字段为项目名称
      this.formEdit02.Name = row.ItemName || row.Name || '检查申请';
      console.log('Dialog state updated, Name set to:', this.formEdit02.Name);

      // 添加错误处理
      this.$nextTick(() => {
        try {
          console.log('DOM updated, dialog should be visible');
        } catch (e) {
          console.error('Dialog open error:', e);
        }
      });
    },
    
    // 新增方法 - 鼠标悬停效果
    handleMouseOver(event) {
      event.target.style.opacity = '0.8';
    },
    
    // 新增方法 - 鼠标移出效果
    handleMouseOut(event) {
      event.target.style.opacity = '1';
    },
    
    //医技操作
    showFme() {
      const hasPatient = this.loadSessionPatient();
      if (hasPatient) {
        this.dialogFormVisible = true;
        this.loadFmeDatas(1, 10, '');
        
        // 确保对话框正确显示
        this.$nextTick(() => {
          if (this.$refs.medicalTechDialog) {
            this.$refs.medicalTechDialog.$el.style.zIndex = '9999';
          }
        });
      } else {
        this.$message({ type: 'warning', message: '请选择已经提交病历的患者' });
        return false;
      }
    },
    searchData() {
      if (this.loadSessionPatient()) {
        this.loading = true;
        this.currentPage01 = 1; // 搜索时重置到第一页
        this.loadFmeDatas(1, this.pageSize, this.keywords01);
      }
    },
    //翻页
    currentChange(currentPage) {
      this.currentPage01 = currentPage;
      this.loading = true;
      this.loadFmeDatas(currentPage, this.pageSize, this.keywords01);
    },
    //加载医技数据
    loadFmeDatas(pageNumber, pageSize, MnemonicCode) {
      let _this = this;
      let url = "/his/medical-tech/list?"
        + "pageNum=" + pageNumber 
        + "&pageSize=" + pageSize 
        + "&keyword=" + encodeURIComponent(MnemonicCode)
        + "&expClassId=3"; // 修改医技分类ID为3
      _this.loading = true;
      console.log('请求URL:', url);
      getReq(url).then(resp => {
        _this.loading = false;
        console.log('响应数据:', resp);
        if (resp.status == 200) {
          console.log('原始数据:', resp.data);
          // 使用正确的数据路径和字段映射
          if (resp.data.records && resp.data.records.length > 0) {
            console.log('第一条原始数据:', resp.data.records[0]);
            _this.categories = resp.data.records.map(item => ({
              ItemCode: item.itemCode,
              ItemName: item.itemName,
              Format: item.format,
              Price: item.price,
              MnemonicCode: item.mnemonicCode,
              ID: item.id
            }));
            console.log('处理后数据:', _this.categories);
            _this.totalCount = resp.data.total;
          } else {
            console.warn('返回数据为空');
            _this.categories = [];
            _this.totalCount = 0;
          }
          _this.currentPage01 = pageNumber;
        } else {
          _this.$message({ type: 'error', message: '数据加载失败!' });
        }
      }).catch(error => {
        _this.loading = false;
        _this.$message({ type: 'error', message: '请求失败: ' + error.message });
      });
    },

    //打开添加模板中医技信息的“对话框”
    handleRowClick(row, column, event) {
      // 如果点击的是选择按钮，则不处理
      if (event.target.classList.contains('el-button')) {
        return;
      }
      this.showInnerDialog(null, row);
    },

    showInnerDialog(index, row) {
      this.innerVisible = true;
      this.templateToApply(row);
    },
    templateToApply(row) {
      this.formEdit02 = row;
      this.formEdit02.CreationTime = new Date();
      this.Temp.TempItemName = row.ItemName;

      this.formEdit02.MedicalID = this.sessionPatient.ID;
      this.formEdit02.RegistID = this.sessionPatient.RegistID;
      this.formEdit02.ItemID = row.ID;
      // 确保Name字段有值
      this.formEdit02.Name = row.ItemName || row.Name || '检查申请';
      this.formEdit02.DoctorID = 1; //默认开立医生的ID为1，需要根据登录医生获取 TODO
      this.formEdit02.CheckOperID = 2; //默认检查人员的ID为2，需要根据排班和工作量获取 TODO
      this.formEdit02.ResultOperID = 2; //默认结果录入人员的ID为2，需要根据获取 TODO
    },
    //
    applyDialog01(val) {
      this.formEdit02.Num = val;
    },
    //添加检查申请
    addCheckDetailed() {
      let _this = this;
      
      // 验证必要字段
      if (!this.sessionPatient?.ID || !this.sessionPatient?.RegistID) {
        this.$message.error('请先选择患者');
        return;
      }

      // 验证申请名称
      if (!this.formEdit02.Name || this.formEdit02.Name.trim() === '') {
        this.$message.error('请填写申请名称');
        return;
      }

      // 设置必要字段并验证
      if (!this.sessionPatient?.ID || !this.sessionPatient?.RegistID) {
        this.$message.error('患者信息不完整，请重新选择患者');
        return;
      }
      
      this.formEdit02.State = '1';
      this.formEdit02.RecordType = '1';
      this.formEdit02.MedicalID = Number(this.sessionPatient.ID);
      this.formEdit02.RegistID = Number(this.sessionPatient.RegistID);
      
      // 深度复制并验证数据
      const submitData = {
        ...JSON.parse(JSON.stringify(this.formEdit02)),
        medicalId: Number(this.sessionPatient.ID),
        registId: Number(this.sessionPatient.RegistID),
        itemId: Number(this.formEdit02.ItemID),
        name: this.formEdit02.Name,
        objective: this.formEdit02.Objective,
        position: this.formEdit02.Position,
        isUrgent: this.formEdit02.IsUrgent ? 1 : 0,
        num: Number(this.formEdit02.Num),
        creationTime: new Date().toISOString(),
        doctorId: Number(this.formEdit02.DoctorID),
        checkOperId: Number(this.formEdit02.CheckOperID),
        resultOperId: Number(this.formEdit02.ResultOperID),
        state: String(this.formEdit02.State),
        recordType: String(this.formEdit02.RecordType)
      };
      
      console.group('检查申请提交调试');
      console.log('完整的提交数据:', submitData);
      console.log('MedicalID验证:', submitData.MedicalID, typeof submitData.MedicalID);
      console.log('RegistID验证:', submitData.RegistID, typeof submitData.RegistID);
      
      // 添加明确的请求配置
      const config = {
        headers: {
          'Content-Type': 'application/json',
          'token': localStorage.getItem('token')
        },
        transformRequest: [
          (data, headers) => {
            console.log('实际发送的请求数据:', data);
            console.log('请求头:', headers);
            return JSON.stringify(data);
          }
        ]
      };
      
      postReq('/his/apply/check', submitData, config)
        .then(resp => {
          console.groupEnd();
          if (resp.status == 200) {
            let json = resp.data;
            _this.$message({ 
              type: 'success',
              message: '检查申请已成功提交！',
              duration: 3000,
              showClose: true
            });
            // 强制刷新数据
            const registID = this.sessionPatient.RegistID || '';
            this.loadApply(registID, '', 1).then(() => {
              this.$forceUpdate(); // 强制视图更新
            });
            
            // 关闭内层对话框
            this.innerVisible = false;
            // 关闭外层对话框
            this.dialogFormVisible = false;
            
            // 刷新医技数据
            this.loadFmeDatas(1, 10, '');
          } else {
            // 显示更详细的错误信息
            const errorMsg = resp.data?.message || resp.statusText;
            _this.$message({ 
              type: 'error', 
              message: `提交失败(${resp.status}): ${errorMsg}`,
              duration: 5000
            });
          }
        })
        .catch(error => {
          // 显示完整的错误信息
          const errorMsg = error.response?.data?.message || error.message;
          _this.$message({ 
            type: 'error', 
            message: `请求失败: ${errorMsg}`,
            duration: 5000
          });
          console.error('提交检查申请错误详情:', {
            request: error.config,
            response: error.response,
            stack: error.stack
          });
        });
    },
    //复选检查项目
    async handleSelectionChange(val) {
      console.log('选中的项目:', val); // 调试日志
      this.selItems = val;
      this.sumPrice = 0;

      for (let i = 0; i < val.length; i++) {
        let price = Number(val[i].Price) || 0;
        console.log(`项目${i+1}: ${val[i].ItemName}, 原始价格: ${price}`); // 调试日志

        // 如果价格为0，尝试从医技项目中获取
        if (price === 0 && (val[i].itemId || val[i].ItemID)) {
          try {
            const itemInfo = await this.getItemPriceById(val[i].itemId || val[i].ItemID);
            if (itemInfo && itemInfo.price) {
              price = Number(itemInfo.price);
              console.log(`从医技项目获取到价格: ${price}`);
              // 更新表格数据中的价格
              val[i].Price = price;
            }
          } catch (error) {
            console.warn('获取项目价格失败:', error);
          }
        }

        console.log(`项目${i+1}: ${val[i].ItemName}, 最终价格: ${price}`); // 调试日志
        this.sumPrice += price;
      }
      console.log('计算后的总金额:', this.sumPrice); // 调试日志
    },
    //删除检查项目
    deleteAll() {
      let tableData02 = this.tableData02;
      let selItems = this.selItems;

      for (let j = 0; j < selItems.length; j++) {
        if (selItems[j].State != 1) {
          this.$message({ type: 'warning', message: '只能删除暂存状态的检查单，请重新选择' });
          return false;
        }
      }

      this.$confirm('确认删除这 ' + this.selItems.length + ' 条数据?', '提示', {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        // let selItems = this.selItems;
        let ids = '';
        for (let i = 0; i < selItems.length; i++) {
          ids += selItems[i].ID + ",";
        }
        this.deleteData(ids);

      }).catch(() => {
        this.$message({ type: 'info', message: '取消' });
      });
    },
    deleteData(ids) {
      let _this = this;
      this.loading = true;
      // 删除 - 使用正确的API路径
      const idArray = ids.split(',').filter(id => id.trim() !== '');
      const deletePromises = idArray.map(id => deleteReq(`/his/apply/${id.trim()}`));

      Promise.all(deletePromises).then(responses => {
        _this.loading = false;
        const successCount = responses.filter(resp => resp.status === 200).length;
        if (successCount === idArray.length) {
          _this.$message({ type: 'success', message: `成功删除${successCount}条记录` });

          // 尝试多种方式获取registerId
          let registerId = _this.$store?.state?.Register?.ID ||
                          _this.sessionPatient?.RegistID || '';

          console.log('删除后准备刷新数据，registerId:', registerId);
          if (registerId) {
            _this.loadApply(registerId, '', 1);
          } else {
            console.warn('无法获取registerId，跳过数据刷新');
          }
        } else {
          _this.$message({ type: 'warning', message: `删除了${successCount}/${idArray.length}条记录` });

          // 尝试多种方式获取registerId
          let registerId = _this.$store?.state?.Register?.ID ||
                          _this.sessionPatient?.RegistID || '';

          console.log('删除后准备刷新数据，registerId:', registerId);
          if (registerId) {
            _this.loadApply(registerId, '', 1);
          } else {
            console.warn('无法获取registerId，跳过数据刷新');
          }
        }
      }).catch(error => {
        _this.loading = false;
        _this.$message({ type: 'error', message: '删除失败: ' + error.message });
      });
    },
    //开立项目
    upToSaved() {
      let selItems = this.selItems;
      this.$confirm('确认开立这 ' + this.selItems.length + ' 条数据?', '提示', {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.updateData(selItems, 2);
      }).catch(() => {
        this.$message({ type: 'info', message: '取消' });
      });
    },
    //作废项目
    upToBad() {
      let selItems = this.selItems;
      for (let j = 0; j < selItems.length; j++) {
        if (selItems[j].State != 2) {
          this.$message({ type: 'warning', message: '只能作废已开立状态的检查单，请重新选择' });
          return false;
        }
      }
      this.$confirm('确认作废这 ' + this.selItems.length + ' 条数据?', '提示', {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.updateData(selItems, 0);
      }).catch(() => {
        this.$message({ type: 'info', message: '取消' });
      });
    },
    //修改状态
    updateData(selItems, state) {
      let _this = this;
      this.loading = true;

      // 批量更新状态 - 使用正确的API路径和JSON格式
      console.log('准备更新的项目:', selItems);
      console.log('目标状态:', state);

      const updatePromises = selItems.map((item, index) => {
        const updateData = {
          ...item,
          id: item.ID,
          state: state
        };
        console.log(`更新数据 ${index + 1}:`, updateData);

        // 创建PUT JSON请求
        return _this.putJsonRequest('/his/apply', updateData);
      });

      Promise.all(updatePromises).then(responses => {
        _this.loading = false;
        console.log('批量更新响应:', responses);

        // 检查每个响应的状态
        const successResponses = responses.filter(resp => {
          console.log('单个响应:', resp);
          return resp && resp.status === 200;
        });

        const successCount = successResponses.length;
        console.log(`成功更新 ${successCount}/${selItems.length} 条记录`);

        if (successCount === selItems.length) {
          const stateText = state === 2 ? '开立' : state === 0 ? '作废' : '更新';
          _this.$message({ type: 'success', message: `成功${stateText}${successCount}条记录` });

          // 如果是开立项目（state === 2），同时发送数据到费用查询
          if (state === 2) {
            _this.sendToFeeInquiry(selItems);
          }

          // 尝试多种方式获取registerId
          let registerId = _this.$store?.state?.Register?.ID ||
                          _this.sessionPatient?.RegistID ||
                          (selItems.length > 0 ? selItems[0].registId : '');

          console.log('准备刷新数据，registerId:', registerId);
          if (registerId) {
            _this.loadApply(registerId, '', 1).then(() => {
              console.log('数据刷新完成');
              _this.$forceUpdate(); // 强制视图更新
            }).catch(error => {
              console.error('数据刷新失败:', error);
            });
          } else {
            console.warn('无法获取registerId，跳过数据刷新');
          }
        } else if (successCount > 0) {
          _this.$message({ type: 'warning', message: `更新了${successCount}/${selItems.length}条记录` });

          // 如果是开立项目，对成功的项目发送费用数据
          if (state === 2) {
            const successItems = selItems.filter((item, index) =>
              successResponses[index] && successResponses[index].status === 200
            );
            if (successItems.length > 0) {
              _this.sendToFeeInquiry(successItems);
            }
          }

          // 尝试多种方式获取registerId
          let registerId = _this.$store?.state?.Register?.ID ||
                          _this.sessionPatient?.RegistID ||
                          (selItems.length > 0 ? selItems[0].registId : '');

          console.log('准备刷新数据，registerId:', registerId);
          if (registerId) {
            _this.loadApply(registerId, '', 1).then(() => {
              console.log('数据刷新完成');
              _this.$forceUpdate(); // 强制视图更新
            }).catch(error => {
              console.error('数据刷新失败:', error);
            });
          } else {
            console.warn('无法获取registerId，跳过数据刷新');
          }
        } else {
          _this.$message({ type: 'error', message: '所有更新请求都失败了' });
        }
      }).catch(error => {
        _this.loading = false;
        console.error('批量更新错误:', error);
        console.error('错误详情:', {
          message: error.message,
          response: error.response,
          request: error.request,
          stack: error.stack
        });

        let errorMessage = '状态更新失败';
        if (error.response) {
          errorMessage += `: ${error.response.status} ${error.response.statusText}`;
          if (error.response.data && error.response.data.message) {
            errorMessage += ` - ${error.response.data.message}`;
          }
        } else if (error.request) {
          errorMessage += ': 网络请求失败';
        } else {
          errorMessage += `: ${error.message}`;
        }

        _this.$message({ type: 'error', message: errorMessage });
      });
    },

    // 发送数据到费用查询系统
    sendToFeeInquiry(items) {
      try {
        console.log('发送数据到费用查询系统:', items);

        // 获取当前患者信息
        const patientInfo = this.getPatientInfo();
        if (!patientInfo) {
          console.warn('患者信息不完整，无法添加费用');
          return;
        }

        // 为每个开立的项目添加费用
        items.forEach(item => {
          try {
            // 构建检验申请费用数据
            const labData = {
              id: item.ID,
              name: item.Name || item.ItemName,
              itemName: item.ItemName || item.Name,
              itemCode: item.ItemCode || '',
              price: parseFloat(item.Price) || 0,
              deptName: item.DeptName || '未分配科室',
              state: item.State || 2, // 已开立状态
              type: 'lab', // 检验类型
              sourceId: item.ID, // 源数据ID
              registId: patientInfo.registId,
              patientId: patientInfo.patientId
            };

            console.log('添加检验费用:', labData);

            // 调用费用工具添加检验申请费用
            addLabApply(labData, patientInfo);

          } catch (error) {
            console.error('添加单个检验费用失败:', error, item);
          }
        });

        // 显示成功消息
        this.$message.success(`已将 ${items.length} 个检验项目添加到费用清单`);

      } catch (error) {
        console.error('发送费用数据失败:', error);
        this.$message.error('添加费用失败: ' + error.message);
      }
    },

    // 获取患者信息
    getPatientInfo() {
      try {
        // 从sessionStorage获取患者信息
        const patientStr = sessionStorage.getItem('medicalRecord');
        if (patientStr && patientStr !== '{}') {
          const patient = JSON.parse(patientStr);
          return {
            registId: patient.RegistID || this.$store?.state?.Register?.ID,
            patientId: patient.ID,
            name: patient.PatientName || this.$store?.state?.Register?.RealName,
            gender: patient.Gender || this.$store?.state?.Register?.Gender,
            age: patient.Age || this.$store?.state?.Register?.Age
          };
        }

        // 从store获取患者信息
        const register = this.$store?.state?.Register;
        if (register && register.ID) {
          return {
            registId: register.ID,
            patientId: register.PatientID,
            name: register.RealName,
            gender: register.Gender,
            age: register.Age
          };
        }

        return null;
      } catch (error) {
        console.error('获取患者信息失败:', error);
        return null;
      }
    },
    //加载申请
    async loadApply(RegistID, State, RecordType) {
      let _this = this;
      if (!RegistID) {
        console.warn('缺少患者登记ID，跳过申请数据加载');
        _this.tableData02 = []; // 设置为空数组
        return Promise.resolve([]); // 返回成功的Promise而不是reject
      }
      let url = "/his/apply/listCheck?" + "RegistID=" + RegistID + "&State=" + State + "&RecordType=" + RecordType;
      return getReq(url).then(async resp => {
        _this.loading = false;
        if (resp.status == 200) {
          console.log('API完整响应:', resp);
          console.log('API响应数据:', resp.data);
          
          // 处理直接返回数组的情况
          let dataList = [];
          if (Array.isArray(resp.data)) {
            dataList = resp.data; // 直接使用数组
          } else if (resp.data && Array.isArray(resp.data.records)) {
            dataList = resp.data.records; // 使用records数组
          }
          
          if (dataList.length > 0) {
            console.log('第一条记录完整结构:', JSON.parse(JSON.stringify(dataList[0])));
            console.log('所有可用字段:', Object.keys(dataList[0]));
          }
          
          // 确保数据完整性和表格渲染安全
          // 确保数据完整性和表格渲染安全
          const processedData = [];
          for (const item of dataList) {
            console.log('原始检查申请数据项:', item);
            console.log('价格相关字段:', {
              price: item.price,
              Price: item.Price,
              itemPrice: item.itemPrice,
              amount: item.amount,
              cost: item.cost
            });

            // 处理价格字段 - 尝试多种可能的价格字段
            let price = 0;
            if (item.price !== null && item.price !== undefined && item.price !== '') {
              price = Number(item.price);
            } else if (item.Price !== null && item.Price !== undefined && item.Price !== '') {
              price = Number(item.Price);
            } else if (item.itemPrice !== null && item.itemPrice !== undefined && item.itemPrice !== '') {
              price = Number(item.itemPrice);
            } else if (item.amount !== null && item.amount !== undefined && item.amount !== '') {
              price = Number(item.amount);
            } else if (item.cost !== null && item.cost !== undefined && item.cost !== '') {
              price = Number(item.cost);
            }

            // 如果价格仍然是0或NaN，尝试从医技项目中获取价格
            if (!price || isNaN(price)) {
              // 尝试多种可能的ItemID字段名
              const itemId = item.itemId || item.ItemID || item.itemid || item.id;
              console.log('尝试获取项目价格，ItemID:', itemId);
              if (itemId) {
                try {
                  const itemInfo = await _this.getItemPriceById(itemId);
                  if (itemInfo && itemInfo.price) {
                    price = Number(itemInfo.price);
                    console.log('从医技项目获取到价格:', price);
                  } else {
                    console.log('未能从医技项目获取到价格信息');
                  }
                } catch (error) {
                  console.warn('获取项目价格失败:', error);
                }
              } else {
                console.log('没有找到ItemID字段');
              }
            }

            // 尝试多种可能的科室字段名
            let deptName = item.deptName ||
                          item.DeptName ||
                          item.departmentName ||
                          item.dept_name ||
                          item.executeDept ||
                          item.executeDeptName;

            // 尝试通过科室ID获取科室名称
            if (!deptName) {
              const deptId = item.deptId || item.DeptId || item.departmentId || item.executeDeptId;
              if (deptId) {
                deptName = _this.getDepartmentNameById(deptId);
                console.log('通过科室ID获取科室名称:', deptId, '->', deptName);
              }
            }

            // 如果仍然没有科室信息，尝试通过项目ID获取
            if (!deptName || deptName === '未分配科室') {
              const itemId = item.itemId || item.ItemID || item.id;
              if (itemId) {
                try {
                  console.log('尝试通过项目ID获取科室:', itemId);
                  deptName = await _this.getDepartmentByItemId(itemId);
                  console.log('通过项目ID获取到科室:', deptName);
                } catch (error) {
                  console.error('获取科室信息失败:', error);
                  deptName = '未分配科室';
                }
              }
            }

            // 最后的默认值
            if (!deptName) {
              deptName = '未分配科室';
            }

            console.log('最终确定的科室名称:', deptName);

            const safeItem = {
              ...item,
              ID: item.id || '',
              Name: item.name || '未知检查',
              ItemName: item.name || '未知项目',
              DeptName: deptName,
              State: item.state || item.State || 0,
              Price: price, // 使用处理后的价格
              Result: item.result || '待检查'
            };

            console.log('处理后检查申请数据项:', {
              ID: safeItem.ID,
              Name: safeItem.Name,
              State: safeItem.State,
              Price: safeItem.Price,
              原始state: item.state,
              原始State: item.State
            });

            console.log('处理后检查申请数据项:', safeItem);
            console.log('处理后Price值:', safeItem.Price, typeof safeItem.Price);

            // 确保所有表格渲染需要的字段都存在
            if (safeItem && typeof safeItem === 'object') {
              Object.keys(safeItem).forEach(key => {
                if (safeItem[key] === undefined || safeItem[key] === null) {
                  safeItem[key] = '';
                }
              });
            }

            processedData.push(safeItem);
          }

          _this.tableData02 = processedData;

          // 调试：检查状态分布
          const stateDistribution = processedData.reduce((acc, item) => {
            const state = item.State || item.state || 0;
            acc[state] = (acc[state] || 0) + 1;
            return acc;
          }, {});
          console.log('状态分布:', stateDistribution);
          console.log('总记录数:', processedData.length);

          // 增强表格列配置的安全性
          if (!_this.tableColumns) {
            _this.tableColumns = [];
          }
          
          _this.tableColumns = _this.tableColumns.map(col => ({
            ...col,
            prop: col.prop || 'Name',
            label: col.label || '检查项目',
            formatter: col.formatter || ((row) => row ? row[col.prop] || '' : '')
          }));
          
          console.log('格式化后的检查申请数据:', JSON.parse(JSON.stringify(_this.tableData02)));
          if (_this.tableData02.length > 0) {
            console.log('映射后的第一条记录:', JSON.parse(JSON.stringify(_this.tableData02[0])));
          }
          return _this.tableData02;
        } else {
          _this.$message({ type: 'error', message: '数据加载失败!' });
          return Promise.reject('数据加载失败');
        }
      }).catch(error => {
        console.error('加载检查申请失败:', error);
        _this.$message({ type: 'error', message: '加载检查申请失败: ' + error.message });
        return Promise.reject(error);
      });
    },
    //加载模板
    loadTemplateByDoc(UserID, RecordType, Name) {
      let _this = this;
      // 修复API调用，使用后端实际支持的参数
      let url = "/his/template/check/listTemplateByDoc?";

      // 根据后端接口，只传递scope和recordType参数
      if (RecordType !== null && RecordType !== undefined && RecordType !== '') {
        url += "recordType=" + RecordType;
      }

      // 如果需要按用户过滤，可能需要使用scope参数
      // 这里先尝试不传scope，获取所有模板

      console.log('加载模板列表，URL:', url);

      return getReq(url).then(resp => {
        _this.loading = false;
        if (resp.status == 200) {
          console.log('模板列表加载成功:', resp.data);
          _this.tableData04 = resp.data.list || resp.data || [];
          console.log('设置模板数据:', _this.tableData04);
          return _this.tableData04;
        } else {
          _this.$message({ type: 'error', message: '模板数据加载失败!' });
          return Promise.reject('模板数据加载失败');
        }
      }).catch(error => {
        _this.loading = false;
        console.error('模板加载错误:', error);
        _this.$message({ type: 'error', message: '请求模板数据失败: ' + error.message });
        return Promise.reject(error);
      });
    },

    // 使用正确的API加载用户模板
    loadTemplateByUser(userId) {
      let _this = this;
      let url = "/his/template/check/user/" + userId;

      console.log('通过用户ID加载模板，URL:', url);

      return getReq(url).then(resp => {
        if (resp.status == 200) {
          console.log('用户模板加载成功:', resp.data);
          _this.tableData04 = resp.data || [];
          console.log('设置用户模板数据:', _this.tableData04);
          console.log('tableData04长度:', _this.tableData04.length);

          // 强制Vue更新
          _this.$nextTick(() => {
            console.log('Vue nextTick - tableData04:', _this.tableData04);
            _this.$forceUpdate();
          });
          return _this.tableData04;
        } else {
          _this.$message({ type: 'error', message: '用户模板数据加载失败!' });
          return Promise.reject('用户模板数据加载失败');
        }
      }).catch(error => {
        console.error('用户模板加载错误:', error);
        _this.$message({ type: 'error', message: '请求用户模板数据失败: ' + error.message });
        return Promise.reject(error);
      });
    },

    //加载患者病历
    loadSessionPatient() {
      let patstr = sessionStorage.getItem('medicalRecord');
      if (patstr != null && patstr.length > 0 && patstr != '{}') {
        this.sessionPatient = JSON.parse(patstr);
        return true;
      } else {
        this.sessionPatient = {
          ID: '', CaseNumber: '', RegistID: '', Readme: '', Present: '', PresentTreat: '', History: '', Allergy: '',
          Physique: '', Proposal: '', Careful: '', CheckResult: '', Diagnosis: '', Handling: '', CaseState: ''
        };
        this.$message({ type: 'info', message: '请选择患者' });
        return false;
      }
    },

    //使用模板添加检查申请
    addTmplChecks(CheckTemp) {
      let _this = this;
      this.$confirm('添加模板中的检查申请,是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        getReq('/his/template/check/listTemplateDetails?templateId=' + CheckTemp.ID + '&expClassId=7').then(resp => {
          if (resp.status == 200) {
            this.categories02 = resp.data.list;
            if (this.categories02 != null) {
              for (let i = 0; i < this.categories02.length; i++) {
                this.templateToApply(this.categories02[i]);
                this.formEdit02.Name = '模板：' + CheckTemp.Name;
                this.formEdit02.ItemID = this.categories02[i].CheckProjID;
                this.formEdit02.Num = 1;
                this.formEdit02.State = '1';
                this.formEdit02.RecordType = '1';
                postReq('/his/apply/check', this.formEdit02).then(resp => {
                  if (resp.status == 200) {
                    let json = resp.data;
                    _this.$message({ type: json.status, message: json.msg });
                    this.loadApply(this.$store.state.Register.ID, '', 1);
                  } else {
                    this.$message({
                      type: 'warning',
                      message: '添加失败!'
                    });
                  }
                }).catch(error => {
                  _this.$message({ type: 'error', message: '提交检查申请失败: ' + error.message });
                });
              }
            }
          } else {
            _this.$message({ type: 'warning', message: '获取模板详情失败!' });
          }
        }).catch(error => {
          _this.$message({ type: 'error', message: '请求模板详情失败: ' + error.message });
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    //显示模板详情
    showTmplChecks(CheckTemp) {
      console.log(CheckTemp.ID);
      this.applyDialogShow = true;
      this.listTmplChecks(CheckTemp.ID)
    },
    
    handleSaveAsTemplate() {
      try {
        if (this.selItems.length === 0) {
          this.$message.warning('请先选择要保存为模板的项目');
          return;
        }
        
        // 验证选择项目的状态
        const invalidItems = this.selItems.filter(item => !item.State);
        if (invalidItems.length > 0) {
          console.warn('部分项目缺少状态:', invalidItems);
          // 为缺少状态的项目设置默认状态
          this.selItems.forEach(item => {
            if (!item.State) {
              item.State = '1'; // 默认暂存状态
            }
          });
        }
        
        this.templateDialogVisible = true;
        this.templateForm = {
          name: '',
          scope: '1' // 默认个人范围
        };
        
        console.log('准备保存为模板的项目:', this.selItems);
      } catch (error) {
        console.error('初始化模板保存对话框错误:', error);
        this.$message.error('初始化模板保存失败: ' + error.message);
      }
    },
    //查询模板详情
    listTmplChecks(CheckTempID) {
      let _this = this;
      getReq('/his/template/check/listTemplateDetails?templateId=' + CheckTempID).then(resp => {
        if (resp.status == 200) {
          this.categories02 = resp.data.list;
        } else {
          _this.$message({ type: 'warning', message: '模板详情加载失败!' });
        }
      }).catch(error => {
        _this.$message({ type: 'error', message: '请求模板详情失败: ' + error.message });
      });
    },
    
    /**
     * 验证模板数据
     */
    validateTemplateData() {
      if (!this.templateForm.name || this.templateForm.name.trim() === '') {
        this.$message.warning('请输入有效的模板名称');
        return false;
      }
      
      if (this.selItems.length === 0) {
        this.$message.warning('请至少选择一个检查项目');
        return false;
      }
      
      return true;
    },
    
    /**
     * 构建模板请求数据
     */
    buildTemplateRequestData() {
      const storeState = this.$store?.state || {};
      const userId = storeState.Register?.UserID || 1;
      
      return {
        name: this.templateForm.name.trim(),
        scope: this.templateForm.scope,
        userId: userId,
        recordType: '1', // 检查类型
        items: this.selItems.map(item => ({
          itemId: item.ID,
          itemName: item.Name,
          itemCode: item.ItemCode || '',
          format: item.Format || '',
          price: item.Price || 0,
          state: item.State || '1' // 默认暂存状态
        }))
      };
    },
    
    /**
     * 处理保存模板错误
     */
    handleSaveTemplateError(error) {
      console.error('保存模板错误详情:', {
        error: error,
        config: error.config,
        response: error.response,
        stack: error.stack
      });
      
      let errorMsg = '保存模板失败';
      if (error.response) {
        // 服务器响应了但状态码不是2xx
        if (error.response.status === 405) {
          errorMsg = '接口请求方法不正确';
        } else if (error.response.status === 400) {
          errorMsg = '请求数据格式错误';
        } else if (error.response.status === 401) {
          errorMsg = '未授权，请重新登录';
        } else if (error.response.status === 500) {
          errorMsg = '服务器内部错误';
        } else {
          errorMsg += `: ${error.response.data?.message || error.response.statusText}`;
        }
      } else if (error.request) {
        // 请求已发出但没有收到响应
        errorMsg = '请求超时或网络错误';
      } else {
        // 其他错误
        errorMsg += `: ${error.message}`;
      }
      
      this.$message.error(errorMsg);
    },
    
    /**
     * 处理保存模板成功
     */
    handleSaveTemplateSuccess(resp, userId) {
      try {
        console.log('模板保存响应:', resp);
        
        // 处理不同格式的成功响应
        let success = false;
        let message = '';
        
        if (typeof resp.data === 'number') {
          success = resp.data === 1;
          message = success ? '模板保存成功' : '模板保存失败';
        } else if (typeof resp.data === 'object') {
          success = resp.data.success;
          message = resp.data.message || (success ? '模板保存成功' : '模板保存失败');
        } else {
          success = false;
          message = '无效的响应格式';
        }
        
        if (success) {
          this.$message.success({
            message: message,
            duration: 2000,
            showClose: true
          });
          this.templateDialogVisible = false;
          console.log('模板保存成功，准备刷新模板列表，userId:', userId);

          // 延迟刷新，确保数据库事务已提交
          setTimeout(() => {
            // 先测试查询所有模板 - 使用正确的API
            console.log('测试查询所有模板...');
            Promise.all([
              getReq('/his/template/check/listTemplateByDoc'),
              getReq('/his/template/check/listTemplateByDoc?recordType=1'),
              getReq('/his/template/check/user/' + userId)
            ]).then(responses => {
              console.log('查询模板响应:', {
                'listTemplateByDoc': responses[0],
                'listTemplateByDoc_recordType1': responses[1],
                'userById': responses[2]
              });
            }).catch(error => {
              console.error('查询模板失败:', error);
            });

            // 使用正确的API刷新模板列表
            this.loadTemplateByUser(userId).then(result => {
              console.log('用户模板刷新完成，结果:', result);
              if (result && result.length > 0) {
                console.log('成功加载用户模板:', result);
                console.log('第一个模板的结构:', result[0]);
                console.log('模板字段检查:', {
                  hasName: 'name' in result[0],
                  hasId: 'id' in result[0],
                  keys: Object.keys(result[0])
                });
              } else {
                console.log('用户暂无模板数据');
              }
            }).catch(error => {
              console.error('用户模板刷新失败:', error);
            });
          }, 1000); // 延迟1秒
          this.$emit('template-saved');
        } else {
          this.$message.error(message);
        }
      } catch (error) {
        console.error('处理保存响应错误:', error);
        this.$message.error('处理保存结果时发生错误');
      }
    },
    
    /**
     * 提交模板表单
     */
    submitTemplateForm() {
      this.$refs.templateFormRef.validate(valid => {
        if (valid) {
          this.saveTemplate();
        } else {
          this.$message.warning('请完善模板信息');
          return false;
        }
      });
    },
    
    /**
     * 取消模板表单
     */
    cancelTemplateForm() {
      if (this.templateSaving) {
        this.$confirm('模板正在保存中，确定要取消吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.resetTemplateForm();
        });
      } else {
        this.resetTemplateForm();
      }
    },
    
    /**
     * 重置模板表单
     */
    resetTemplateForm() {
      this.$refs.templateFormRef.resetFields();
      this.templateDialogVisible = false;
      this.templateSaving = false;
    },
    
    /**
     * 保存模板主方法
     */
    saveTemplate() {
      try {
        // 验证数据
        if (!this.validateTemplateData()) {
          return;
        }
        
        // 准备数据
        const templateData = this.buildTemplateRequestData();
        const userId = templateData.userId;
        console.log('模板保存 - userId:', userId, 'templateData:', templateData);
        
        // 设置加载状态
        this.loading = true;
        this.templateSaving = true;
        
        // 配置请求参数
        const config = {
          timeout: 10000, // 10秒超时
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          retry: 1, // 重试次数
          retryDelay: 1000, // 重试延迟
          responseType: 'json' // 期望的响应类型
        };
        
        // 发送请求（带重试机制）
        const sendRequest = (attempt = 0) => {
          return new Promise((resolve, reject) => {
            postJsonRequest('/his/template/check', templateData, config)
              .then(resp => {
                resolve(resp);
                this.handleSaveTemplateSuccess(resp, userId);
              })
              .catch(error => {
                if (attempt < config.retry && (!error.response || error.code === 'ECONNABORTED')) {
                  console.log(`请求超时，第${attempt + 1}次重试...`);
                  setTimeout(() => sendRequest(attempt + 1).then(resolve).catch(reject), config.retryDelay);
                } else {
                  reject(error);
                  this.handleSaveTemplateError(error);
                }
              });
          });
        };
        
        return sendRequest()
          .catch(error => {
            console.error('保存模板最终错误:', error);
            throw error; // 继续向上抛出错误
          })
          .finally(() => {
            this.loading = false;
            this.templateSaving = false;
          });
      } catch (error) {
        console.error('保存模板异常:', error);
        this.$message.error('保存模板时发生错误: ' + error.message);
        this.loading = false;
        this.templateSaving = false;
      }
    },

    // 加载科室信息
    async loadDepartments() {
      console.log('开始加载科室信息...');

      // 尝试多种科室API
      const apiUrls = [
        '/department/page?count=200&pn=1',
        '/his/department/list',
        '/his/dept/list',
        '/department/list',
        '/dept/page?count=200&pn=1',
        '/dept/list',
        '/his/department/page?count=200&pn=1'
      ];

      for (const url of apiUrls) {
        try {
          console.log('尝试科室API:', url);
          const response = await getReq(url);
          console.log('科室API响应:', response);

          if (response.status === 200) {
            let deptList = [];

            // 处理不同的响应格式
            if (response.data.records) {
              deptList = response.data.records;
            } else if (response.data.list) {
              deptList = response.data.list;
            } else if (Array.isArray(response.data)) {
              deptList = response.data;
            }

            console.log('科室列表数据:', deptList);

            if (deptList.length > 0) {
              this.departments = deptList.reduce((map, dept) => {
                console.log('处理科室:', dept);
                const deptId = dept.id || dept.deptId || dept.departmentId;
                const deptName = dept.deptName || dept.name || dept.departmentName;
                if (deptId && deptName) {
                  map[deptId] = deptName;
                }
                return map;
              }, {});

              console.log('科室信息加载成功:', this.departments);
              console.log('科室数量:', Object.keys(this.departments).length);
              return; // 成功加载，退出循环
            }
          }
        } catch (error) {
          // 只记录非404错误
          if (error.response?.status !== 404) {
            console.error(`科室API ${url} 失败:`, error.message);
          } else {
            console.log(`科室API ${url} 不存在 (404)`);
          }
        }
      }

      // 如果所有API都失败，添加一些测试科室数据
      console.log('所有科室API都失败，添加测试科室数据');
      this.departments = {
        1: '内科',
        2: '外科',
        3: '儿科',
        4: '妇产科',
        5: '急诊科',
        6: '放射科',
        7: '检验科',
        8: '超声科',
        9: '心电图室',
        10: 'ICU'
      };
      console.log('使用测试科室数据:', this.departments);
    },

    // 根据科室ID获取科室名称
    getDepartmentNameById(deptId) {
      if (!deptId) return '未分配科室';
      return this.departments[deptId] || '未分配科室';
    },

    // 根据项目ID获取科室信息
    async getDepartmentByItemId(itemId) {
      try {
        console.log('尝试获取项目科室信息，ItemID:', itemId);

        // 首先尝试获取完整的项目信息
        const itemDetail = await this.getItemDetailById(itemId);
        if (itemDetail) {
          console.log('获取到项目详情:', itemDetail);
          console.log('项目所有字段:', Object.keys(itemDetail));

          // 基于后端SQL查询，优先检查这些字段
          let deptName = itemDetail.deptName || itemDetail.departmentName || itemDetail.dept_name ||
                        itemDetail.department_name || itemDetail.executeDept || itemDetail.execute_dept;
          let deptId = itemDetail.deptId || itemDetail.departmentId || itemDetail.dept_id ||
                      itemDetail.department_id || itemDetail.executeDeptId || itemDetail.execute_dept_id;

          // 检查是否有关联的科室对象
          if (itemDetail.department) {
            deptName = itemDetail.department.deptName || itemDetail.department.name || itemDetail.department.departmentName;
            deptId = itemDetail.department.id || itemDetail.department.deptId;
          }

          console.log('科室字段检查:', { deptName, deptId });

          // 如果有科室ID但没有科室名称，通过ID获取名称
          if (!deptName && deptId) {
            deptName = this.getDepartmentNameById(deptId);
            console.log('通过科室ID获取名称:', deptId, '->', deptName);
          }

          // 如果找到有效的科室名称
          if (deptName && deptName !== '未分配科室') {
            console.log('从项目详情获取到科室:', deptName);
            return deptName;
          }

          // 尝试从项目分类推断科室
          if (itemDetail.categoryName || itemDetail.category) {
            const category = itemDetail.categoryName || itemDetail.category;
            const deptMapping = {
              '放射': '放射科',
              'CT': '放射科',
              'MRI': '放射科',
              'X光': '放射科',
              '超声': '超声科',
              'B超': '超声科',
              '心电': '心电图室',
              'ECG': '心电图室',
              '检验': '检验科',
              '化验': '检验科',
              '血液': '检验科',
              '尿液': '检验科'
            };

            for (const [key, dept] of Object.entries(deptMapping)) {
              if (category.includes(key)) {
                console.log('通过分类推断科室:', category, '->', dept);
                return dept;
              }
            }
          }
        }

      } catch (error) {
        console.error('获取项目科室信息失败:', error);
      }

      console.log('无法获取项目科室信息，返回未分配科室');
      return '未分配科室';
    },

    // 通过项目ID获取完整项目信息（包括科室）
    async getItemDetailById(itemId) {
      console.log('获取项目详情，ID:', itemId);

      try {
        // 方法1：从医技项目列表中搜索（避免404错误）
        console.log('从医技项目列表中搜索...');
        const response = await getReq(`/his/medical-tech/list?pageNum=1&pageSize=100&expClassId=7`);
        if (response.status === 200 && response.data.records) {
          console.log('医技项目列表获取成功，总数:', response.data.records.length);
          const item = response.data.records.find(item => item.id == itemId);
          if (item) {
            console.log('从列表中找到项目:', item);
            console.log('项目所有字段:', Object.keys(item));
            return item;
          } else {
            console.log(`项目ID ${itemId} 在列表中未找到`);
            // 显示所有可用的项目ID
            const availableIds = response.data.records.map(item => item.id);
            console.log('可用的项目ID列表:', availableIds);
          }
        }
      } catch (error) {
        console.log('医技项目列表搜索失败:', error.message);
      }

      try {
        // 方法2：尝试fmeditem API
        console.log('尝试fmeditem API...');
        const response = await getReq(`/fmeditem/page?count=100&pn=1`);
        if (response.status === 200 && response.data?.data?.records) {
          const records = response.data.data.records;
          console.log('fmeditem列表获取成功，总数:', records.length);
          const item = records.find(item => item.id == itemId);
          if (item) {
            console.log('从fmeditem中找到项目:', item);
            console.log('fmeditem项目所有字段:', Object.keys(item));
            return item;
          } else {
            console.log(`项目ID ${itemId} 在fmeditem中未找到`);
            // 显示所有可用的项目ID
            const availableIds = records.map(item => item.id);
            console.log('fmeditem可用的项目ID列表:', availableIds.slice(0, 10), '...(显示前10个)');
          }
        }
      } catch (error) {
        console.log('fmeditem API搜索失败:', error.message);
      }

      console.log(`无法找到项目ID ${itemId} 的详细信息`);
      return null;
    },

    // 调试科室数据
    async debugDepartmentData() {
      console.log('=== 科室数据调试信息 ===');
      console.log('当前科室数据:', this.departments);
      console.log('当前科室数量:', Object.keys(this.departments).length);

      // 重新加载科室数据
      console.log('重新加载科室数据...');
      await this.loadDepartments();
      console.log('重新加载后的科室数据:', this.departments);
      console.log('重新加载后的科室数量:', Object.keys(this.departments).length);

      // 测试项目科室获取 - 使用实际存在的项目
      console.log('开始测试项目科室获取...');

      // 首先获取可用的项目列表
      try {
        const itemResponse = await getReq('/his/medical-tech/list?pageNum=1&pageSize=5&expClassId=7');
        if (itemResponse.status === 200 && itemResponse.data.records && itemResponse.data.records.length > 0) {
          const firstAvailableItem = itemResponse.data.records[0];
          const itemId = firstAvailableItem.id;

          console.log('使用实际存在的项目进行测试:');
          console.log('项目ID:', itemId);
          console.log('项目名称:', firstAvailableItem.itemName);
          console.log('项目完整信息:', firstAvailableItem);

          // 测试科室获取
          const deptName = await this.getDepartmentByItemId(itemId);
          console.log('最终获取到的科室名称:', deptName);

          // 如果有申请数据，也测试申请项目
          if (this.tableData02 && this.tableData02.length > 0) {
            const firstApplyItem = this.tableData02[0];
            console.log('申请项目信息:', {
              DeptName: firstApplyItem.DeptName,
              ItemID: firstApplyItem.ItemID,
              ID: firstApplyItem.ID
            });
          }

          // 显示详细的调试结果
          this.$alert(`
            科室调试详细结果:

            科室数据加载:
            - 科室数量: ${Object.keys(this.departments).length}
            - 科室列表: ${Object.values(this.departments).join(', ') || '无'}

            项目测试:
            - 测试项目ID: ${itemId}
            - 项目名称: ${firstAvailableItem.itemName}
            - 获取到的科室: ${deptName}

            医技项目数据:
            - 医技项目数量: ${this.categories?.length || 0}

            详细调试信息请查看浏览器控制台
          `, '科室调试信息', {
            confirmButtonText: '确定'
          }).catch(() => {
            // 用户取消弹窗，忽略错误
          });

        } else {
          this.$alert('无法获取医技项目列表进行测试', '科室调试信息').catch(() => {});
        }
      } catch (error) {
        console.error('科室调试失败:', error);
        this.$alert(`
          科室调试失败:

          错误信息: ${error.message}
          科室数量: ${Object.keys(this.departments).length}

          详细错误信息请查看浏览器控制台
        `, '科室调试信息', {
          confirmButtonText: '确定'
        }).catch(() => {});
      }
    },
  },
  data() {
    return {
      loading: false,
      dialogTableVisible: false,
      dialogFormVisible: false,
      innerVisible: false,
      applyDialogShow: false,
      categories: [],
      categories02: [],
      pageSize: 10,
      totalCount: 0,
      currentPage01: 1,
      keywords01: '',
      formEdit02: {
        ID: '', MedicalID: '', RegistID: '', ItemID: '', Name: '', Objective: '', Position: '', IsUrgent: false, Num: 1, CreationTime: new Date(),
        DoctorID: '', CheckOperID: '', ResultOperID: '', CheckTime: new Date(), Result: '', ResultTime: new Date(), State: '1', RecordType: '1'
      },
      tableData02: [],
      tableData04: [],
      selItems: [],
      departments: {}, // 科室数据映射
      //病历信息 -- sessionStorage中
      sessionPatient: {
        ID: '', CaseNumber: '', RegistID: '', Readme: '', Present: '', PresentTreat: '', History: '', Allergy: '',
        Physique: '', Proposal: '', Careful: '', CheckResult: '', Diagnosis: '', Handling: '', CaseState: ''
      },
      Temp: { TempItemName: '' },
      sumPrice: 0,

      formEdit03: { ID: '', Name: '', Scope: '', UserID: '1', CreationTime: new Date(), RecordType: '' },
      dialogForm01: { ID: '', Name: '', Scope: '1', UserID: '1', CreationTime: '2019-03-08 10:18', RecordType: '1' },
      searchForm01: { Name: '', Scope: '', RecordType: '' },
      templateData01: [],
      templateDialogVisible: false,
      templateForm: {
        name: '',
        scope: '1'
      },
      templateSaving: false, // 模板保存加载状态
      templateRules: {
        name: [
          { required: true, message: '请输入模板名称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在2到20个字符', trigger: 'blur' }
        ],
        scope: [
          { required: true, message: '请选择模板范围', trigger: 'change' }
        ]
      },
      templateFormRef: null, // 表单引用
    }
  },
  filters: {
    showState: function (state) {
      if (state == '1') {
        return '暂存';
      } else if (state == '2') {
        return '已开立';
      } else if (state == '3') {
        return '已交费';
      } else if (state == '4') {
        return '已登记';
      } else if (state == '5') {
        return '执行完';
      } else if (state == '6') {
        return '已退费';
      } else {
        return '已作废';
      }
    },

  },
}
</script>

<!-- <style scoped>
/* 调试样式 - 对话框可见性检查 */
.medical-tech-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
  border: 3px dashed red !important;
  background: white !important;
  min-width: 500px !important;
  max-height: 90vh !important;
  overflow: auto !important;
}

/* 蒙层样式 */
.el-overlay {
  z-index: 9998 !important;
  background-color: rgba(0,0,0,0.5) !important;
}

/* 确保父容器不限制对话框 */
.container >>> .el-dialog {
  position: static !important;
  overflow: visible !important;
}
</style> -->